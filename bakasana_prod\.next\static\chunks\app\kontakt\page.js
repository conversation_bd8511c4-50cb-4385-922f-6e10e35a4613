/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/kontakt/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5CContactForm.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Ccomponents%5C%5CPerformantWhatsApp.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5CContactForm.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Ccomponents%5C%5CPerformantWhatsApp.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/kontakt/ContactForm.jsx */ \"(app-pages-browser)/./src/app/kontakt/ContactForm.jsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/components/PerformantWhatsApp.jsx */ \"(app-pages-browser)/./src/components/PerformantWhatsApp.jsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyQyUzQSU1QyU1Q1VzZXJzJTVDJTVDZGF2aWQlNUMlNUNEZXNrdG9wJTVDJTVDUHJvamVrdHklNUMlNUNiYWthc2FuYV9wcm9kJTVDJTVDYmFrYXNhbmFfcHJvZCU1QyU1Q3NyYyU1QyU1Q2FwcCU1QyU1Q2tvbnRha3QlNUMlNUNDb250YWN0Rm9ybS5qc3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjJkZWZhdWx0JTIyJTVEJTdEJm1vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkMlM0ElNUMlNUNVc2VycyU1QyU1Q2RhdmlkJTVDJTVDRGVza3RvcCU1QyU1Q1Byb2pla3R5JTVDJTVDYmFrYXNhbmFfcHJvZCU1QyU1Q2Jha2FzYW5hX3Byb2QlNUMlNUNzcmMlNUMlNUNjb21wb25lbnRzJTVDJTVDUGVyZm9ybWFudFdoYXRzQXBwLmpzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw0TEFBdUs7QUFDdks7QUFDQSx3TUFBNEsiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiwgd2VicGFja0V4cG9ydHM6IFtcImRlZmF1bHRcIl0gKi8gXCJDOlxcXFxVc2Vyc1xcXFxkYXZpZFxcXFxEZXNrdG9wXFxcXFByb2pla3R5XFxcXGJha2FzYW5hX3Byb2RcXFxcYmFrYXNhbmFfcHJvZFxcXFxzcmNcXFxcYXBwXFxcXGtvbnRha3RcXFxcQ29udGFjdEZvcm0uanN4XCIpO1xuO1xuaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIsIHdlYnBhY2tFeHBvcnRzOiBbXCJkZWZhdWx0XCJdICovIFwiQzpcXFxcVXNlcnNcXFxcZGF2aWRcXFxcRGVza3RvcFxcXFxQcm9qZWt0eVxcXFxiYWthc2FuYV9wcm9kXFxcXGJha2FzYW5hX3Byb2RcXFxcc3JjXFxcXGNvbXBvbmVudHNcXFxcUGVyZm9ybWFudFdoYXRzQXBwLmpzeFwiKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5CContactForm.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Ccomponents%5C%5CPerformantWhatsApp.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcZGF2aWRcXERlc2t0b3BcXFByb2pla3R5XFxiYWthc2FuYV9wcm9kXFxiYWthc2FuYV9wcm9kXFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/kontakt/ContactForm.jsx":
/*!*****************************************!*\
  !*** ./src/app/kontakt/ContactForm.jsx ***!
  \*****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ContactForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ContactForm() {\n    _s();\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        name: '',\n        email: '',\n        phone: '',\n        message: '',\n        retreatInterest: '',\n        honeypot: ''\n    });\n    const [status, setStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [isSubmitting, setIsSubmitting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [touched, setTouched] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    // Validation functions\n    const validateField = (name, value)=>{\n        switch(name){\n            case 'name':\n                return value.length < 2 ? 'Imię musi mieć co najmniej 2 znaki' : '';\n            case 'email':\n                const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n                return !emailRegex.test(value) ? 'Podaj prawidłowy adres email' : '';\n            case 'phone':\n                const phoneRegex = /^[\\+]?[0-9\\s\\-\\(\\)]{9,}$/;\n                return value && !phoneRegex.test(value) ? 'Podaj prawidłowy numer telefonu' : '';\n            case 'message':\n                return value.length < 10 ? 'Wiadomość musi mieć co najmniej 10 znaków' : '';\n            default:\n                return '';\n        }\n    };\n    const handleChange = (e)=>{\n        const { id, value } = e.target;\n        setFormData({\n            ...formData,\n            [id]: value\n        });\n        // Real-time validation\n        if (touched[id]) {\n            const error = validateField(id, value);\n            setErrors({\n                ...errors,\n                [id]: error\n            });\n        }\n    };\n    const handleBlur = (e)=>{\n        const { id, value } = e.target;\n        setTouched({\n            ...touched,\n            [id]: true\n        });\n        const error = validateField(id, value);\n        setErrors({\n            ...errors,\n            [id]: error\n        });\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        // Honeypot check - jeśli wypełnione, to spam\n        if (formData.honeypot) {\n            return;\n        }\n        // Validate all fields\n        const newErrors = {};\n        Object.keys(formData).forEach((field)=>{\n            if (field !== 'honeypot' && field !== 'phone' && field !== 'retreatInterest') {\n                const error = validateField(field, formData[field]);\n                if (error) newErrors[field] = error;\n            }\n        });\n        if (Object.keys(newErrors).length > 0) {\n            setErrors(newErrors);\n            setTouched(Object.keys(formData).reduce((acc, key)=>({\n                    ...acc,\n                    [key]: true\n                }), {}));\n            setStatus('Proszę poprawić błędy w formularzu');\n            return;\n        }\n        setIsSubmitting(true);\n        setStatus('Wysyłanie...');\n        try {\n            const response = await fetch('https://api.web3forms.com/submit', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Accept': 'application/json'\n                },\n                body: JSON.stringify({\n                    access_key: \"your-web3forms-access-key\" || 0,\n                    name: formData.name,\n                    email: formData.email,\n                    phone: formData.phone,\n                    message: formData.message,\n                    retreat_interest: formData.retreatInterest,\n                    subject: `Nowa wiadomość z BAKASANA od ${formData.name}`,\n                    from_name: 'BAKASANA',\n                    to_email: '<EMAIL>',\n                    // Additional metadata\n                    source: 'contact_form',\n                    timestamp: new Date().toISOString(),\n                    user_agent: navigator.userAgent\n                })\n            });\n            const result = await response.json();\n            if (result.success) {\n                setStatus('✅ Wiadomość wysłana pomyślnie! Odpowiemy w ciągu 24 godzin.');\n                setFormData({\n                    name: '',\n                    email: '',\n                    phone: '',\n                    message: '',\n                    retreatInterest: '',\n                    honeypot: ''\n                });\n                setErrors({});\n                setTouched({});\n                // Track successful form submission\n                if ( true && window.gtag) {\n                    window.gtag('event', 'form_submit', {\n                        event_category: 'Contact',\n                        event_label: 'Contact Form Success'\n                    });\n                }\n            } else {\n                throw new Error(result.message || 'Błąd wysyłania');\n            }\n        } catch (error) {\n            console.error('Error:', error);\n            setStatus('Wystąpił błąd. Spróbuj ponownie lub napisz bezpoś<NAME_EMAIL>');\n        } finally{\n            setIsSubmitting(false);\n            setTimeout(()=>setStatus(''), 8000);\n        }\n    };\n    const socialLinks = [\n        {\n            href: \"https://www.instagram.com/fly_with_bakasana?igsh=MWpmanNpeHVodTlubA%3D%3D&utm_source=qr\",\n            label: \"Instagram\",\n            aria: \"Profil na Instagramie\"\n        },\n        {\n            href: \"https://www.facebook.com/p/Fly-with-bakasana-100077568306563/\",\n            label: \"Facebook\",\n            aria: \"Profil na Facebooku\"\n        },\n        {\n            href: \"mailto:<EMAIL>\",\n            label: \"Email\",\n            aria: \"Kontakt email\"\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 lg:grid-cols-2 gap-20 items-start max-w-5xl mx-auto\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center lg:text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                className: \"section-header mb-6\",\n                                children: \"Napisz do nas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 156,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"body-text opacity-80\",\n                                children: \"Każda wiadomość jest dla nas ważna\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 157,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 155,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n                        onSubmit: handleSubmit,\n                        className: \"space-y-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"name\",\n                                        className: \"block subtle-text mb-3\",\n                                        children: \"Imię *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 164,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"text\",\n                                        id: \"name\",\n                                        value: formData.name,\n                                        onChange: handleChange,\n                                        onBlur: handleBlur,\n                                        required: true,\n                                        className: `w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${errors.name ? 'border-red-500' : 'border-stone/30 focus:border-temple-gold'} focus:outline-none`,\n                                        placeholder: \"Twoje imię\",\n                                        \"aria-describedby\": errors.name ? 'name-error' : undefined\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 165,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.name && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        id: \"name-error\",\n                                        className: \"text-red-500 text-sm mt-2\",\n                                        children: errors.name\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 179,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 163,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"email\",\n                                        className: \"block subtle-text mb-3\",\n                                        children: \"Email *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 184,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"email\",\n                                        id: \"email\",\n                                        value: formData.email,\n                                        onChange: handleChange,\n                                        onBlur: handleBlur,\n                                        required: true,\n                                        className: `w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${errors.email ? 'border-red-500' : 'border-stone/30 focus:border-temple-gold'} focus:outline-none`,\n                                        placeholder: \"<EMAIL>\",\n                                        \"aria-describedby\": errors.email ? 'email-error' : undefined\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 185,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.email && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        id: \"email-error\",\n                                        className: \"text-red-500 text-sm mt-2\",\n                                        children: errors.email\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 199,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 183,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"phone\",\n                                        className: \"block subtle-text mb-3\",\n                                        children: \"Telefon (opcjonalnie)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 204,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"tel\",\n                                        id: \"phone\",\n                                        value: formData.phone,\n                                        onChange: handleChange,\n                                        onBlur: handleBlur,\n                                        className: `w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 ${errors.phone ? 'border-red-500' : 'border-stone/30 focus:border-temple-gold'} focus:outline-none`,\n                                        placeholder: \"+48 123 456 789\",\n                                        \"aria-describedby\": errors.phone ? 'phone-error' : undefined\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 205,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.phone && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        id: \"phone-error\",\n                                        className: \"text-red-500 text-sm mt-2\",\n                                        children: errors.phone\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 218,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 203,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"retreatInterest\",\n                                        className: \"block subtle-text mb-3\",\n                                        children: \"Interesuje Cię (opcjonalnie)\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 223,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                        id: \"retreatInterest\",\n                                        value: formData.retreatInterest,\n                                        onChange: handleChange,\n                                        className: \"w-full px-0 py-4 bg-transparent border-0 border-b border-stone/30 focus:border-temple-gold focus:outline-none transition-colors text-charcoal\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"\",\n                                                children: \"Wybierz opcję\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                lineNumber: 230,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"retreat-bali\",\n                                                children: \"Retreat na Bali\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                lineNumber: 231,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"retreat-poland\",\n                                                children: \"Retreat w Polsce\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                lineNumber: 232,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"private-sessions\",\n                                                children: \"Sesje indywidualne\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                lineNumber: 233,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"online-classes\",\n                                                children: \"Zajęcia online\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                lineNumber: 234,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"workshops\",\n                                                children: \"Warsztaty\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                value: \"other\",\n                                                children: \"Inne\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                                lineNumber: 236,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 222,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                        htmlFor: \"message\",\n                                        className: \"block subtle-text mb-3\",\n                                        children: \"Wiadomość *\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"textarea\", {\n                                        id: \"message\",\n                                        value: formData.message,\n                                        onChange: handleChange,\n                                        onBlur: handleBlur,\n                                        required: true,\n                                        rows: 6,\n                                        className: `w-full px-0 py-4 bg-transparent border-0 border-b transition-colors text-charcoal placeholder-stone/50 resize-none ${errors.message ? 'border-red-500' : 'border-stone/30 focus:border-temple-gold'} focus:outline-none`,\n                                        placeholder: \"Podziel się swoimi myślami...\",\n                                        \"aria-describedby\": errors.message ? 'message-error' : undefined\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 242,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.message && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        id: \"message-error\",\n                                        className: \"text-red-500 text-sm mt-2\",\n                                        children: errors.message\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 240,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                id: \"honeypot\",\n                                name: \"honeypot\",\n                                value: formData.honeypot,\n                                onChange: handleChange,\n                                style: {\n                                    display: 'none'\n                                },\n                                tabIndex: \"-1\",\n                                autoComplete: \"off\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 261,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"pt-8\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        type: \"submit\",\n                                        disabled: isSubmitting,\n                                        className: `btn-ghost btn-primary ${isSubmitting ? 'opacity-50 cursor-not-allowed' : ''}`,\n                                        children: isSubmitting ? 'Wysyłanie...' : 'Wyślij Wiadomość'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 273,\n                                        columnNumber: 13\n                                    }, this),\n                                    status && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-charcoal/70 font-light mt-4 max-w-xs\",\n                                        children: status\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 282,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 272,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 162,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                lineNumber: 154,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"space-y-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center lg:text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                className: \"section-header mb-6\",\n                                children: \"Znajdź nas\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"body-text opacity-80 mb-8\",\n                                children: \"Połączmy się w przestrzeni cyfrowej\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 294,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 292,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: socialLinks.map((link)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: link.href,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                \"aria-label\": link.aria,\n                                className: \"block p-6 hover:opacity-70 transition-opacity duration-200 text-center lg:text-left\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                        className: \"font-light text-charcoal mb-2 tracking-wide text-lg\",\n                                        children: link.label\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-stone font-light\",\n                                        children: [\n                                            link.label === 'Instagram' && 'Codzienne inspiracje',\n                                            link.label === 'Facebook' && 'Społeczność BAKASANA',\n                                            link.label === 'Email' && 'Bezpośredni kontakt'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                        lineNumber: 312,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, link.label, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 301,\n                                columnNumber: 13\n                            }, this))\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 299,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-center lg:justify-start my-12\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-4 text-temple-gold/60\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-px bg-temple-gold/30\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-lg opacity-60\",\n                                    children: \"ॐ\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                    lineNumber: 325,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-12 h-px bg-temple-gold/30\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                    lineNumber: 326,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                            lineNumber: 323,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 322,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center lg:text-left\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-sm text-stone font-light italic tracking-wide\",\n                                children: '\"Każda podr\\xf3ż zaczyna się od jednego kroku...\"'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 331,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xs text-temple-gold font-light tracking-wide uppercase mt-2\",\n                                children: \"Om Swastiastu\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                                lineNumber: 334,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                        lineNumber: 330,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n                lineNumber: 291,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\app\\\\kontakt\\\\ContactForm.jsx\",\n        lineNumber: 152,\n        columnNumber: 5\n    }, this);\n}\n_s(ContactForm, \"EUfibYDvMLFOunoxKHR5V4elYI4=\");\n_c = ContactForm;\nvar _c;\n$RefreshReg$(_c, \"ContactForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/kontakt/ContactForm.jsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/PerformantWhatsApp.jsx":
/*!***********************************************!*\
  !*** ./src/components/PerformantWhatsApp.jsx ***!
  \***********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n// Inline SVG WhatsApp icon - no external image requests\nconst WhatsAppIcon = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.memo)((param)=>{\n    let { className = \"w-5 h-5\" } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n        className: className,\n        fill: \"currentColor\",\n        viewBox: \"0 0 24 24\",\n        xmlns: \"http://www.w3.org/2000/svg\",\n        \"aria-hidden\": \"true\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n            d: \"M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.488\"\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n            lineNumber: 14,\n            columnNumber: 5\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n        lineNumber: 7,\n        columnNumber: 3\n    }, undefined);\n});\n_c = WhatsAppIcon;\nWhatsAppIcon.displayName = 'WhatsAppIcon';\n// Simplified WhatsApp component - minimal tracking\nconst PerformantWhatsApp = /*#__PURE__*/ _s((0,react__WEBPACK_IMPORTED_MODULE_1__.memo)(_c1 = _s((param)=>{\n    let { className = '', size = 'md', message = 'Cześć! Interesuję się retreatami jogowymi na Bali lub Sri Lance. Czy możesz mi przesłać więcej informacji?', variant = 'button' } = param;\n    _s();\n    const phoneNumber = '48606101523';\n    // Simplified analytics - only essential tracking\n    const handleClick = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PerformantWhatsApp.useCallback[handleClick]\": ()=>{\n            // GA4 event\n            if ( true && window.gtag) {\n                window.gtag('event', 'whatsapp_click', {\n                    event_category: 'engagement',\n                    event_label: 'whatsapp_contact',\n                    value: 1\n                });\n            }\n            // FB Pixel Contact event\n            if ( true && window.fbq) {\n                window.fbq('track', 'Contact');\n            }\n        }\n    }[\"PerformantWhatsApp.useCallback[handleClick]\"], []);\n    const sizeClasses = {\n        sm: 'w-4 h-4',\n        md: 'w-5 h-5',\n        lg: 'w-6 h-6'\n    };\n    const buttonClasses = {\n        sm: 'h-10 w-10',\n        md: 'h-12 w-12',\n        lg: 'h-14 w-14'\n    };\n    const whatsappUrl = `https://wa.me/${phoneNumber}?text=${encodeURIComponent(message)}`;\n    // Icon variant for navbar/footer\n    if (variant === 'icon') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n            href: whatsappUrl,\n            target: \"_blank\",\n            rel: \"noopener noreferrer\",\n            onClick: handleClick,\n            className: `text-enterprise-brown hover:text-terra transition-colors duration-300 ${className}`,\n            \"aria-label\": \"Skontaktuj się przez WhatsApp\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WhatsAppIcon, {\n                className: sizeClasses[size]\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n                lineNumber: 71,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n            lineNumber: 63,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Floating variant for QuickCTA\n    if (variant === 'floating') {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"fixed bottom-6 right-6 z-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                href: whatsappUrl,\n                target: \"_blank\",\n                rel: \"noopener noreferrer\",\n                onClick: handleClick,\n                className: `\n            bg-green-500 hover:bg-green-600 text-white \n            w-14 h-14 rounded-full\n            transition-all duration-300 \n            hover:scale-110 focus:outline-none focus:ring-2 focus:ring-green-500/50\n            focus:ring-offset-2 shadow-lg hover:shadow-xl\n            flex items-center justify-center\n            animate-pulse hover:animate-none\n            ${className}\n          `,\n                \"aria-label\": \"Skontaktuj się przez WhatsApp\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WhatsAppIcon, {\n                    className: \"w-6 h-6\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n                    lineNumber: 97,\n                    columnNumber: 11\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n                lineNumber: 80,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n            lineNumber: 79,\n            columnNumber: 7\n        }, undefined);\n    }\n    // Default button\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n        href: whatsappUrl,\n        target: \"_blank\",\n        rel: \"noopener noreferrer\",\n        onClick: handleClick,\n        className: `\n        bg-enterprise-brown hover:bg-enterprise-brown/90 text-white \n        ${buttonClasses[size]} rounded-full\n        transition-all duration-300 \n        hover:scale-105 focus:outline-none focus:ring-2 focus:ring-enterprise-brown/50\n        focus:ring-offset-2 shadow-lg hover:shadow-xl\n        flex items-center justify-center\n        ${className}\n      `,\n        \"aria-label\": \"Skontaktuj się przez WhatsApp\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(WhatsAppIcon, {\n            className: sizeClasses[size]\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n            lineNumber: 121,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\Projekty\\\\bakasana_prod\\\\bakasana_prod\\\\src\\\\components\\\\PerformantWhatsApp.jsx\",\n        lineNumber: 105,\n        columnNumber: 5\n    }, undefined);\n}, \"PRIOWs9bezaAbp8UlGmbaZMoYYA=\")), \"PRIOWs9bezaAbp8UlGmbaZMoYYA=\");\n_c2 = PerformantWhatsApp;\nPerformantWhatsApp.displayName = 'PerformantWhatsApp';\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (PerformantWhatsApp);\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"WhatsAppIcon\");\n$RefreshReg$(_c1, \"PerformantWhatsApp$memo\");\n$RefreshReg$(_c2, \"PerformantWhatsApp\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/PerformantWhatsApp.jsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Capp%5C%5Ckontakt%5C%5CContactForm.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22C%3A%5C%5CUsers%5C%5Cdavid%5C%5CDesktop%5C%5CProjekty%5C%5Cbakasana_prod%5C%5Cbakasana_prod%5C%5Csrc%5C%5Ccomponents%5C%5CPerformantWhatsApp.jsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);