'use client';

import React from 'react';
import { cn } from '@/lib/utils';

/**
 * UnifiedFeedback - Spójny system komunikatów zwrotnych
 * Obsługuje: success, error, warning, info, loading
 */

// Success Message Component
export function SuccessMessage({ children, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "flex items-center gap-3 p-4 bg-green-50 border border-green-200 text-green-800",
        "transition-all duration-300 ease-smooth",
        className
      )}
      role="status"
      aria-live="polite"
      {...props}
    >
      <svg className="w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
      </svg>
      <div className="font-inter font-light text-sm">
        {children}
      </div>
    </div>
  );
}

// Error Message Component
export function ErrorMessage({ children, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "flex items-center gap-3 p-4 bg-red-50 border border-red-200 text-red-800",
        "transition-all duration-300 ease-smooth",
        className
      )}
      role="alert"
      aria-live="assertive"
      {...props}
    >
      <svg className="w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
      </svg>
      <div className="font-inter font-light text-sm">
        {children}
      </div>
    </div>
  );
}

// Warning Message Component
export function WarningMessage({ children, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "flex items-center gap-3 p-4 bg-yellow-50 border border-yellow-200 text-yellow-800",
        "transition-all duration-300 ease-smooth",
        className
      )}
      role="alert"
      aria-live="polite"
      {...props}
    >
      <svg className="w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
      </svg>
      <div className="font-inter font-light text-sm">
        {children}
      </div>
    </div>
  );
}

// Info Message Component
export function InfoMessage({ children, className = '', ...props }) {
  return (
    <div 
      className={cn(
        "flex items-center gap-3 p-4 bg-blue-50 border border-blue-200 text-blue-800",
        "transition-all duration-300 ease-smooth",
        className
      )}
      role="status"
      aria-live="polite"
      {...props}
    >
      <svg className="w-5 h-5 flex-shrink-0" fill="currentColor" viewBox="0 0 20 20">
        <path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" />
      </svg>
      <div className="font-inter font-light text-sm">
        {children}
      </div>
    </div>
  );
}

// Loading Message Component
export function LoadingMessage({ children = "Ładowanie...", className = '', ...props }) {
  return (
    <div 
      className={cn(
        "flex items-center gap-3 p-4 bg-sanctuary border border-stone-light text-charcoal",
        "transition-all duration-300 ease-smooth",
        className
      )}
      role="status"
      aria-live="polite"
      {...props}
    >
      <svg className="w-5 h-5 flex-shrink-0 animate-spin" fill="none" viewBox="0 0 24 24">
        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="2"/>
        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"/>
      </svg>
      <div className="font-inter font-light text-sm">
        {children}
      </div>
    </div>
  );
}

// Universal Feedback Component
export function UnifiedFeedback({ 
  type = 'info', 
  children, 
  className = '', 
  ...props 
}) {
  const components = {
    success: SuccessMessage,
    error: ErrorMessage,
    warning: WarningMessage,
    info: InfoMessage,
    loading: LoadingMessage
  };
  
  const Component = components[type] || InfoMessage;
  
  return (
    <Component className={className} {...props}>
      {children}
    </Component>
  );
}

// Toast Notification Component
export function UnifiedToast({ 
  type = 'info',
  children,
  isVisible = false,
  onClose,
  duration = 4000,
  className = ''
}) {
  const [show, setShow] = React.useState(isVisible);
  
  React.useEffect(() => {
    setShow(isVisible);
    
    if (isVisible && duration > 0) {
      const timer = setTimeout(() => {
        setShow(false);
        setTimeout(() => onClose?.(), 300);
      }, duration);
      
      return () => clearTimeout(timer);
    }
  }, [isVisible, duration, onClose]);
  
  if (!show) return null;
  
  const baseClasses = cn(
    "fixed top-4 right-4 z-50 max-w-md",
    "transform transition-all duration-300 ease-smooth",
    show ? "translate-x-0 opacity-100" : "translate-x-full opacity-0",
    className
  );
  
  return (
    <div className={baseClasses}>
      <UnifiedFeedback type={type}>
        <div className="flex items-center justify-between w-full">
          <span className="flex-1">{children}</span>
          <button
            onClick={() => {
              setShow(false);
              setTimeout(() => onClose?.(), 300);
            }}
            className="ml-4 text-current opacity-70 hover:opacity-100 transition-opacity duration-200 focus:outline-none focus:opacity-100"
            aria-label="Zamknij powiadomienie"
          >
            <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
      </UnifiedFeedback>
    </div>
  );
}

// Hook for managing toasts
export function useUnifiedToast() {
  const [toasts, setToasts] = React.useState([]);
  
  const showToast = React.useCallback((message, type = 'info', duration = 4000) => {
    const id = Date.now();
    const newToast = { id, message, type, duration };
    
    setToasts(prev => [...prev, newToast]);
  }, []);
  
  const removeToast = React.useCallback((id) => {
    setToasts(prev => prev.filter(toast => toast.id !== id));
  }, []);
  
  const ToastContainer = React.useCallback(() => (
    <div className="fixed top-4 right-4 z-50 space-y-2">
      {toasts.map(toast => (
        <UnifiedToast
          key={toast.id}
          type={toast.type}
          duration={toast.duration}
          isVisible={true}
          onClose={() => removeToast(toast.id)}
        >
          {toast.message}
        </UnifiedToast>
      ))}
    </div>
  ), [toasts, removeToast]);
  
  return {
    showToast,
    ToastContainer
  };
}

export default UnifiedFeedback;
